#!/usr/bin/env python3
"""
PRD Generator Demo - Offline Version
Demonstrates the conversation flow without API calls
"""

import asyncio
from datetime import datetime
from typing import Dict, List, Any

class PRDDemoBot:
    """Demo version of PRD bot that works without API calls"""
    
    def __init__(self):
        self.conversation_step = 0
        self.user_responses = []
        self.prd_data = {
            "executive_summary": "",
            "background_context": "",
            "product_definition": "",
            "functional_requirements": "",
            "non_functional_requirements": "",
            "technical_requirements": "",
            "user_experience": "",
            "implementation_plan": "",
            "success_criteria": "",
            "constraints_assumptions": ""
        }
        
        self.questions = [
            "Hello! I'm here to help you create a comprehensive Product Requirements Document (PRD). Let's start with your product vision. In one sentence, what is the core product you want to build and what problem does it solve?",
            
            "Great! Now let's dive into the problem. Describe the specific problem your users face today. What are they currently doing to solve this, and why isn't it working?",
            
            "What's the business context? Is this: 1) A completely new product, 2) Adding features to existing product, 3) Improving current functionality, or 4) Replacing an existing system?",
            
            "Who is your target audience? Please describe your primary users including their roles, demographics, technical expertise, and current pain points.",
            
            "Let's map out what your product needs to DO. What are the core actions users will take? List the 5-7 most important features or capabilities.",
            
            "You've mentioned several features. If you could only ship 3 features in the first version, which would they be? Rank them 1-3 by user impact.",
            
            "Beyond functionality, what are your performance, security, and reliability requirements? Consider factors like response times, user capacity, security standards, and uptime expectations.",
            
            "What are your technical preferences and constraints? This includes platform choices (web/mobile/desktop), integration requirements, data storage needs, and any existing systems to connect with.",
            
            "Describe your vision for the user experience. What should the interface feel like? Are there any design preferences, accessibility requirements, or user workflow considerations?",
            
            "How do you want to approach building this? Would you prefer: 1) MVP with core features first, 2) Phased release over time, or 3) Full feature set at once? Also, what's your target timeline?",
            
            "How will you measure success? What specific metrics or outcomes would indicate that your product is achieving its goals? Include both user-focused and business metrics.",
            
            "What constraints should I be aware of? This includes budget limitations, timeline pressures, technical constraints, regulatory requirements, or key assumptions you're making.",
            
            "We've covered all the major areas for your PRD. Is there anything else you'd like to add or any section you'd like to revisit before I generate the final document?"
        ]
        
        self.section_mapping = [
            "executive_summary",
            "executive_summary", 
            "background_context",
            "product_definition",
            "functional_requirements",
            "functional_requirements",
            "non_functional_requirements",
            "technical_requirements",
            "user_experience",
            "implementation_plan",
            "success_criteria",
            "constraints_assumptions",
            "final_review"
        ]
    
    async def start_conversation(self):
        """Start the demo conversation"""
        print("🚀 Welcome to the AI-Powered PRD Generator Demo!")
        print("=" * 55)
        print("📝 This demo shows the conversation flow without API calls")
        print("💡 Your responses will be used to build a comprehensive PRD")
        print()
        
        return self.questions[0]
    
    async def process_response(self, user_input: str) -> str:
        """Process user response and return next question"""
        if not user_input.strip():
            return "Please provide a response to continue."
        
        # Store the response
        self.user_responses.append(user_input)
        
        # Map response to PRD section
        if self.conversation_step < len(self.section_mapping):
            section = self.section_mapping[self.conversation_step]
            if section in self.prd_data:
                if self.prd_data[section]:
                    self.prd_data[section] += f"\n\n{user_input}"
                else:
                    self.prd_data[section] = user_input
        
        # Move to next step
        self.conversation_step += 1
        
        # Check if conversation is complete
        if self.conversation_step >= len(self.questions):
            return await self.generate_final_prd()
        
        # Return next question
        return self.questions[self.conversation_step]
    
    async def generate_final_prd(self) -> str:
        """Generate the final PRD document"""
        prd_content = f"""
# Product Requirements Document

**Generated on:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**Generated by:** PRD Generator Demo

---

## 1. Executive Summary
{self.prd_data.get('executive_summary', 'Not provided')}

## 2. Background & Context
{self.prd_data.get('background_context', 'Not provided')}

## 3. Product Definition
{self.prd_data.get('product_definition', 'Not provided')}

## 4. Functional Requirements
{self.prd_data.get('functional_requirements', 'Not provided')}

## 5. Non-Functional Requirements
{self.prd_data.get('non_functional_requirements', 'Not provided')}

## 6. Technical Requirements
{self.prd_data.get('technical_requirements', 'Not provided')}

## 7. User Experience & Design
{self.prd_data.get('user_experience', 'Not provided')}

## 8. Implementation Plan
{self.prd_data.get('implementation_plan', 'Not provided')}

## 9. Success Criteria & Metrics
{self.prd_data.get('success_criteria', 'Not provided')}

## 10. Constraints & Assumptions
{self.prd_data.get('constraints_assumptions', 'Not provided')}

---
*Document generated by PRD Generator Demo*
        """
        
        # Save to file
        filename = f"demo_prd_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(prd_content.strip())
        
        return f"""🎉 Congratulations! Your PRD has been generated successfully!

📄 **PRD saved to:** {filename}

✅ **Sections completed:** {len([v for v in self.prd_data.values() if v])} / 10

Here's your completed PRD:

{prd_content}

Thank you for using the PRD Generator Demo! 
To use the full AI-powered version, ensure you have a valid Google Gemini API key and run prd.py instead."""

class DemoInterface:
    """Simple demo interface"""
    
    def __init__(self):
        self.bot = PRDDemoBot()
        self.session_active = False
    
    async def start_session(self):
        """Start demo session"""
        try:
            initial_message = await self.bot.start_conversation()
            print(f"🤖 AI: {initial_message}")
            self.session_active = True
            
            await self._conversation_loop()
            
        except Exception as e:
            print(f"❌ Error: {str(e)}")
    
    async def _conversation_loop(self):
        """Main conversation loop"""
        while self.session_active:
            try:
                # Get user input
                user_input = input("\n👤 You: ").strip()
                
                if not user_input:
                    continue
                
                # Handle special commands
                if user_input.lower() in ['quit', 'exit', 'bye']:
                    print("\n👋 Demo session ended. Thank you!")
                    break
                
                # Process response
                print("\n🤔 Processing your response...")
                ai_response = await self.bot.process_response(user_input)
                print(f"\n🤖 AI: {ai_response}")
                
                # Check if conversation is complete
                if "Congratulations! Your PRD has been generated" in ai_response:
                    break
                    
            except KeyboardInterrupt:
                print("\n\n👋 Demo interrupted by user.")
                break
            except Exception as e:
                print(f"\n❌ Error: {str(e)}")

async def main():
    """Run the demo"""
    print("🔧 PRD Generator Demo")
    print("=" * 25)
    print("This is a demonstration version that works without API calls.")
    print("For the full AI-powered experience, use prd.py with a valid API key.")
    print()
    
    interface = DemoInterface()
    await interface.start_session()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Error: {str(e)}")
