import os
import json
import asyncio
from typing import Dict, List, Optional, Any, TypedDict, Annotated
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum

# Lang<PERSON>hain imports
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain.prompts import ChatPromptTemplate
from langchain.schema import HumanMessage, AIMessage
from langchain.memory import ConversationBufferMemory
from langchain_core.messages import BaseMessage

# LangGraph imports
from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver
from langgraph.prebuilt import ToolExecutor

# Pydantic for data validation
from pydantic import BaseModel, Field

# Environment setup
os.environ["GOOGLE_API_KEY"] = "your-gemini-api-key-here"

class ConversationState(TypedDict):
    """State object for the conversation graph"""
    messages: Annotated[List[BaseMessage], "The conversation messages"]
    current_node: str
    prd_data: Dict[str, Any]
    retry_count: int
    user_context: Dict[str, Any]
    validation_flags: Dict[str, bool]
    completeness_score: float

class NodeType(Enum):
    """Types of conversation nodes"""
    DISCOVERY = "discovery"
    REQUIREMENTS = "requirements"
    VALIDATION = "validation"
    PLANNING = "planning"
    FINALIZATION = "finalization"

class QuestionType(Enum):
    """Types of questions for intelligent routing"""
    OPEN_ENDED = "open_ended"
    YES_NO = "yes_no"
    MULTIPLE_CHOICE = "multiple_choice"
    PRIORITY_RANKING = "priority_ranking"
    VALIDATION = "validation"

@dataclass
class ConversationNode:
    """Represents a node in the conversation tree"""
    id: str
    name: str
    node_type: NodeType
    question: str
    question_type: QuestionType
    required_context: List[str] = field(default_factory=list)
    validation_rules: List[str] = field(default_factory=list)
    fallback_questions: List[str] = field(default_factory=list)
    next_nodes: Dict[str, str] = field(default_factory=dict)
    prd_section: str = ""
    max_retries: int = 3

class PRDSection(BaseModel):
    """Pydantic model for PRD sections"""
    title: str
    content: str
    completeness: float = 0.0
    last_updated: datetime = Field(default_factory=datetime.now)

class PRDDocument(BaseModel):
    """Complete PRD document structure"""
    executive_summary: PRDSection = PRDSection(title="Executive Summary", content="")
    background_context: PRDSection = PRDSection(title="Background & Context", content="")
    product_definition: PRDSection = PRDSection(title="Product Definition", content="")
    functional_requirements: PRDSection = PRDSection(title="Functional Requirements", content="")
    non_functional_requirements: PRDSection = PRDSection(title="Non-Functional Requirements", content="")
    technical_requirements: PRDSection = PRDSection(title="Technical Requirements", content="")
    user_experience: PRDSection = PRDSection(title="User Experience & Design", content="")
    implementation_plan: PRDSection = PRDSection(title="Implementation Plan", content="")
    success_criteria: PRDSection = PRDSection(title="Success Criteria & Metrics", content="")
    constraints_assumptions: PRDSection = PRDSection(title="Constraints & Assumptions", content="")
    
    def get_completeness_score(self) -> float:
        """Calculate overall document completeness"""
        sections = [self.executive_summary, self.background_context, self.product_definition,
                   self.functional_requirements, self.non_functional_requirements,
                   self.technical_requirements, self.user_experience, self.implementation_plan,
                   self.success_criteria, self.constraints_assumptions]
        
        total_score = sum(section.completeness for section in sections)
        return total_score / len(sections)

class IntelligenceEngine:
    """AI intelligence layer for conversation management"""
    
    def __init__(self, llm):
        self.llm = llm
        self.context_analyzer = self._create_context_analyzer()
        self.response_processor = self._create_response_processor()
        self.validation_engine = self._create_validation_engine()
    
    def _create_context_analyzer(self):
        """Create context analysis prompt"""
        return ChatPromptTemplate.from_messages([
            ("system", """You are an expert product analyst. Analyze the user's response and context to determine:
            1. Response clarity (1-10)
            2. Completeness (1-10)
            3. Detected product type/domain
            4. User expertise level (beginner/intermediate/expert)
            5. Required follow-up questions
            6. Risk flags or concerns
            
            Return analysis as JSON format."""),
            ("human", "Context: {context}\nUser Response: {response}")
        ])
    
    def _create_response_processor(self):
        """Create response processing prompt"""
        return ChatPromptTemplate.from_messages([
            ("system", """You are a PRD expert. Process the user's response to extract structured information for the PRD.
            
            Extract relevant information and format it appropriately for the {prd_section} section.
            Consider the conversation context and ensure consistency.
            
            Return the processed information in a structured format."""),
            ("human", "PRD Section: {prd_section}\nConversation Context: {context}\nUser Response: {response}")
        ])
    
    def _create_validation_engine(self):
        """Create validation prompt"""
        return ChatPromptTemplate.from_messages([
            ("system", """You are a quality assurance expert for PRDs. Validate the provided information for:
            1. Completeness
            2. Consistency
            3. Feasibility
            4. Missing critical elements
            5. Potential conflicts
            
            Return validation results with specific recommendations."""),
            ("human", "PRD Section: {section}\nContent to validate: {content}")
        ])
    
    async def analyze_context(self, context: str, response: str) -> Dict[str, Any]:
        """Analyze conversation context and user response"""
        try:
            prompt = self.context_analyzer.format(context=context, response=response)
            result = await self.llm.ainvoke(prompt)
            return json.loads(result.content)
        except Exception as e:
            return {
                "clarity": 5,
                "completeness": 5,
                "product_type": "unknown",
                "expertise_level": "intermediate",
                "follow_up_needed": True,
                "error": str(e)
            }
    
    async def process_response(self, prd_section: str, context: str, response: str) -> str:
        """Process user response for PRD content"""
        prompt = self.response_processor.format(
            prd_section=prd_section,
            context=context,
            response=response
        )
        result = await self.llm.ainvoke(prompt)
        return result.content
    
    async def validate_content(self, section: str, content: str) -> Dict[str, Any]:
        """Validate PRD content quality"""
        prompt = self.validation_engine.format(section=section, content=content)
        result = await self.llm.ainvoke(prompt)
        try:
            return json.loads(result.content)
        except:
            return {"validation_passed": True, "recommendations": []}

class ConversationTreeBuilder:
    """Builds the conversation tree structure"""
    
    @staticmethod
    def build_tree() -> Dict[str, ConversationNode]:
        """Build the complete conversation tree"""
        nodes = {}
        
        # Discovery Phase Nodes
        nodes["start"] = ConversationNode(
            id="start",
            name="Introduction",
            node_type=NodeType.DISCOVERY,
            question="Hello! I'm here to help you create a comprehensive Product Requirements Document (PRD). Let's start with your product vision. In one sentence, what is the core product you want to build and what problem does it solve?",
            question_type=QuestionType.OPEN_ENDED,
            next_nodes={"default": "vision_analysis"},
            prd_section="executive_summary"
        )
        
        nodes["vision_analysis"] = ConversationNode(
            id="vision_analysis",
            name="Vision Analysis",
            node_type=NodeType.DISCOVERY,
            question="I need to better understand your vision. Can you describe the main user action or workflow your product enables?",
            question_type=QuestionType.OPEN_ENDED,
            fallback_questions=[
                "What's the primary task users will accomplish with your product?",
                "If you had to explain your product to a 10-year-old, how would you do it?"
            ],
            next_nodes={"clear": "problem_deep_dive", "unclear": "vision_clarification"},
            prd_section="executive_summary"
        )
        
        nodes["vision_clarification"] = ConversationNode(
            id="vision_clarification",
            name="Vision Clarification",
            node_type=NodeType.DISCOVERY,
            question="Let me help clarify. Which of these categories best describes your product: 1) Business/Enterprise Tool, 2) Consumer Application, 3) Educational Platform, 4) E-commerce Solution, 5) Other?",
            question_type=QuestionType.MULTIPLE_CHOICE,
            next_nodes={"answered": "problem_deep_dive"},
            prd_section="executive_summary",
            max_retries=2
        )
        
        nodes["problem_deep_dive"] = ConversationNode(
            id="problem_deep_dive",
            name="Problem Analysis",
            node_type=NodeType.DISCOVERY,
            question="Now let's dive into the problem. Describe the specific problem your users face today. What are they currently doing to solve this, and why isn't it working?",
            question_type=QuestionType.OPEN_ENDED,
            next_nodes={"detailed": "business_context", "vague": "problem_specificity"},
            prd_section="executive_summary"
        )
        
        nodes["problem_specificity"] = ConversationNode(
            id="problem_specificity",
            name="Problem Specificity",
            node_type=NodeType.DISCOVERY,
            question="Let's get specific. Can you give me a concrete example of when this problem occurred for a real user? Include details about the situation, what went wrong, and the impact.",
            question_type=QuestionType.OPEN_ENDED,
            fallback_questions=[
                "Think of the last time you or someone you know experienced this problem. Walk me through what happened.",
                "What would happen if this problem was never solved?"
            ],
            next_nodes={"specific": "business_context"},
            prd_section="executive_summary"
        )
        
        nodes["business_context"] = ConversationNode(
            id="business_context",
            name="Business Context",
            node_type=NodeType.DISCOVERY,
            question="What's the business context? Is this: 1) A completely new product, 2) Adding features to existing product, 3) Improving current functionality, or 4) Replacing an existing system?",
            question_type=QuestionType.MULTIPLE_CHOICE,
            next_nodes={"new_product": "market_analysis", "feature_addition": "existing_product_context", "improvement": "performance_gap"},
            prd_section="background_context"
        )
        
        nodes["market_analysis"] = ConversationNode(
            id="market_analysis",
            name="Market Analysis",
            node_type=NodeType.DISCOVERY,
            question="For a new product, I need to understand the market. Who are your main competitors, and what alternatives do users currently have?",
            question_type=QuestionType.OPEN_ENDED,
            next_nodes={"complete": "target_audience"},
            prd_section="background_context"
        )
        
        nodes["target_audience"] = ConversationNode(
            id="target_audience",
            name="Target Audience",
            node_type=NodeType.DISCOVERY,
            question="Who is your target audience? Please describe your primary users including their roles, demographics, technical expertise, and current pain points.",
            question_type=QuestionType.OPEN_ENDED,
            next_nodes={"complete": "functional_requirements"},
            prd_section="product_definition"
        )
        
        # Requirements Phase Nodes
        nodes["functional_requirements"] = ConversationNode(
            id="functional_requirements",
            name="Functional Requirements",
            node_type=NodeType.REQUIREMENTS,
            question="Let's map out what your product needs to DO. What are the core actions users will take? List the 5-7 most important features or capabilities.",
            question_type=QuestionType.OPEN_ENDED,
            next_nodes={"complete": "feature_prioritization"},
            prd_section="functional_requirements"
        )
        
        nodes["feature_prioritization"] = ConversationNode(
            id="feature_prioritization",
            name="Feature Prioritization",
            node_type=NodeType.REQUIREMENTS,
            question="You've mentioned several features. If you could only ship 3 features in the first version, which would they be? Rank them 1-3 by user impact.",
            question_type=QuestionType.PRIORITY_RANKING,
            next_nodes={"prioritized": "non_functional_requirements"},
            prd_section="functional_requirements"
        )
        
        nodes["non_functional_requirements"] = ConversationNode(
            id="non_functional_requirements",
            name="Non-Functional Requirements",
            node_type=NodeType.REQUIREMENTS,
            question="Beyond functionality, what are your performance, security, and reliability requirements? Consider factors like response times, user capacity, security standards, and uptime expectations.",
            question_type=QuestionType.OPEN_ENDED,
            next_nodes={"complete": "technical_requirements"},
            prd_section="non_functional_requirements"
        )
        
        nodes["technical_requirements"] = ConversationNode(
            id="technical_requirements",
            name="Technical Requirements",
            node_type=NodeType.REQUIREMENTS,
            question="What are your technical preferences and constraints? This includes platform choices (web/mobile/desktop), integration requirements, data storage needs, and any existing systems to connect with.",
            question_type=QuestionType.OPEN_ENDED,
            next_nodes={"complete": "user_experience"},
            prd_section="technical_requirements"
        )
        
        nodes["user_experience"] = ConversationNode(
            id="user_experience",
            name="User Experience",
            node_type=NodeType.REQUIREMENTS,
            question="Describe your vision for the user experience. What should the interface feel like? Are there any design preferences, accessibility requirements, or user workflow considerations?",
            question_type=QuestionType.OPEN_ENDED,
            next_nodes={"complete": "validation_phase"},
            prd_section="user_experience"
        )
        
        # Validation Phase Nodes
        nodes["validation_phase"] = ConversationNode(
            id="validation_phase",
            name="Requirements Validation",
            node_type=NodeType.VALIDATION,
            question="Let me validate what I've captured. I'll summarize your key requirements. Please confirm if this accurately represents your vision and let me know if anything is missing or incorrect.",
            question_type=QuestionType.VALIDATION,
            next_nodes={"confirmed": "implementation_planning", "corrections": "requirement_corrections"},
            prd_section="validation"
        )
        
        nodes["requirement_corrections"] = ConversationNode(
            id="requirement_corrections",
            name="Requirement Corrections",
            node_type=NodeType.VALIDATION,
            question="Thank you for the feedback. Please specify what needs to be corrected or added to ensure the PRD accurately reflects your requirements.",
            question_type=QuestionType.OPEN_ENDED,
            next_nodes={"corrected": "implementation_planning"},
            prd_section="validation"
        )
        
        # Planning Phase Nodes
        nodes["implementation_planning"] = ConversationNode(
            id="implementation_planning",
            name="Implementation Planning",
            node_type=NodeType.PLANNING,
            question="How do you want to approach building this? Would you prefer: 1) MVP with core features first, 2) Phased release over time, or 3) Full feature set at once? Also, what's your target timeline?",
            question_type=QuestionType.MULTIPLE_CHOICE,
            next_nodes={"answered": "success_metrics"},
            prd_section="implementation_plan"
        )
        
        nodes["success_metrics"] = ConversationNode(
            id="success_metrics",
            name="Success Metrics",
            node_type=NodeType.PLANNING,
            question="How will you measure success? What specific metrics or outcomes would indicate that your product is achieving its goals? Include both user-focused and business metrics.",
            question_type=QuestionType.OPEN_ENDED,
            next_nodes={"complete": "constraints_assumptions"},
            prd_section="success_criteria"
        )
        
        nodes["constraints_assumptions"] = ConversationNode(
            id="constraints_assumptions",
            name="Constraints and Assumptions",
            node_type=NodeType.PLANNING,
            question="What constraints should I be aware of? This includes budget limitations, timeline pressures, technical constraints, regulatory requirements, or key assumptions you're making.",
            question_type=QuestionType.OPEN_ENDED,
            next_nodes={"complete": "final_review"},
            prd_section="constraints_assumptions"
        )
        
        # Finalization Phase
        nodes["final_review"] = ConversationNode(
            id="final_review",
            name="Final Review",
            node_type=NodeType.FINALIZATION,
            question="We've covered all the major areas for your PRD. Is there anything else you'd like to add or any section you'd like to revisit before I generate the final document?",
            question_type=QuestionType.OPEN_ENDED,
            next_nodes={"complete": "generate_prd", "revisions": "handle_revisions"},
            prd_section="finalization"
        )
        
        nodes["generate_prd"] = ConversationNode(
            id="generate_prd",
            name="Generate PRD",
            node_type=NodeType.FINALIZATION,
            question="Perfect! I'll now generate your comprehensive PRD based on our conversation. This may take a moment...",
            question_type=QuestionType.VALIDATION,
            next_nodes={"complete": "end"},
            prd_section="document_generation"
        )
        
        return nodes

class PRDConversationBot:
    """Main conversation bot for PRD generation"""
    
    def __init__(self, api_key: str):
        self.llm = ChatGoogleGenerativeAI(
            model="gemini-pro",
            google_api_key=api_key,
            temperature=0.7
        )
        self.intelligence_engine = IntelligenceEngine(self.llm)
        self.conversation_tree = ConversationTreeBuilder.build_tree()
        self.memory = ConversationBufferMemory(return_messages=True)
        self.prd_document = PRDDocument()
        
        # Initialize LangGraph
        self.graph = self._create_conversation_graph()
        self.checkpointer = MemorySaver()
    
    def _create_conversation_graph(self) -> StateGraph:
        """Create the LangGraph conversation flow"""
        
        def should_continue(state: ConversationState) -> str:
            """Determine next node based on current state"""
            current_node = self.conversation_tree[state["current_node"]]
            
            # Check retry logic
            if state["retry_count"] >= current_node.max_retries:
                return "fallback_handler"
            
            # Analyze response and determine next step
            return "continue_conversation"
        
        async def process_user_input(state: ConversationState) -> ConversationState:
            """Process user input and update state"""
            current_node_id = state["current_node"]
            current_node = self.conversation_tree[current_node_id]
            
            if state["messages"]:
                user_response = state["messages"][-1].content
                
                # Analyze response with AI
                context = self._get_conversation_context(state)
                analysis = await self.intelligence_engine.analyze_context(context, user_response)
                
                # Process response for PRD content
                if current_node.prd_section:
                    processed_content = await self.intelligence_engine.process_response(
                        current_node.prd_section, context, user_response
                    )
                    
                    # Update PRD document
                    self._update_prd_section(current_node.prd_section, processed_content)
                
                # Determine next node
                next_node_id = self._determine_next_node(current_node, analysis, user_response)
                state["current_node"] = next_node_id
                
                # Reset retry count if successful
                if analysis.get("clarity", 0) > 6:
                    state["retry_count"] = 0
                else:
                    state["retry_count"] += 1
            
            return state
        
        async def generate_response(state: ConversationState) -> ConversationState:
            """Generate AI response based on current node"""
            current_node_id = state["current_node"]
            current_node = self.conversation_tree.get(current_node_id)
            
            if not current_node:
                # End of conversation
                if current_node_id == "generate_prd":
                    prd_content = await self._generate_final_prd(state)
                    response = f"Here's your completed PRD:\n\n{prd_content}"
                else:
                    response = "Thank you for using the PRD generator!"
            else:
                # Generate contextual question
                response = await self._generate_contextual_question(current_node, state)
            
            # Add AI response to messages
            state["messages"].append(AIMessage(content=response))
            return state
        
        async def handle_fallback(state: ConversationState) -> ConversationState:
            """Handle fallback scenarios"""
            current_node = self.conversation_tree[state["current_node"]]
            
            if current_node.fallback_questions:
                fallback_question = current_node.fallback_questions[0]
                state["messages"].append(AIMessage(content=fallback_question))
                state["retry_count"] = 0
            else:
                # Skip to next logical node
                next_node_id = list(current_node.next_nodes.values())[0]
                state["current_node"] = next_node_id
                state["retry_count"] = 0
            
            return state
        
        # Build the graph
        workflow = StateGraph(ConversationState)
        
        # Add nodes
        workflow.add_node("process_input", process_user_input)
        workflow.add_node("generate_response", generate_response)
        workflow.add_node("fallback_handler", handle_fallback)
        
        # Add edges
        workflow.add_edge("process_input", "generate_response")
        workflow.add_conditional_edges(
            "generate_response",
            should_continue,
            {
                "continue_conversation": "process_input",
                "fallback_handler": "fallback_handler",
                "end": END
            }
        )
        workflow.add_edge("fallback_handler", "generate_response")
        
        # Set entry point
        workflow.set_entry_point("generate_response")
        
        return workflow.compile(checkpointer=self.checkpointer)
    
    def _get_conversation_context(self, state: ConversationState) -> str:
        """Extract conversation context for AI analysis"""
        messages = state["messages"][-5:]  # Last 5 messages for context
        context_parts = []
        
        for msg in messages:
            role = "User" if isinstance(msg, HumanMessage) else "AI"
            context_parts.append(f"{role}: {msg.content}")
        
        return "\n".join(context_parts)
    
    def _determine_next_node(self, current_node: ConversationNode, analysis: Dict, response: str) -> str:
        """Determine next conversation node based on analysis"""
        # Default next node logic
        if current_node.question_type == QuestionType.YES_NO:
            if "yes" in response.lower() or "y" == response.lower().strip():
                return current_node.next_nodes.get("yes", current_node.next_nodes.get("default", "end"))
            else:
                return current_node.next_nodes.get("no", current_node.next_nodes.get("default", "end"))
        
        elif current_node.question_type == QuestionType.MULTIPLE_CHOICE:
            # Simple number detection for multiple choice
            for i in range(1, 6):
                if str(i) in response:
                    return current_node.next_nodes.get(f"option_{i}", current_node.next_nodes.get("answered", "end"))
            return current_node.next_nodes.get("answered", "end")
        
        else:
            # For open-ended questions, use AI analysis
            clarity = analysis.get("clarity", 5)
            completeness = analysis.get("completeness", 5)
            
            if clarity >= 7 and completeness >= 6:
                return current_node.next_nodes.get("complete", current_node.next_nodes.get("clear", current_node.next_nodes.get("default", "end")))
            elif clarity < 5:
                return current_node.next_nodes.get("unclear", current_node.next_nodes.get("default", "end"))
            else:
                return current_node.next_nodes.get("incomplete", current_node.next_nodes.get("default", "end"))
    
    async def _generate_contextual_question(self, node: ConversationNode, state: ConversationState) -> str:
        """Generate contextual question based on conversation state"""
        base_question = node.question
        
        # Add context-aware modifications
        user_context = state.get("user_context", {})
        product_type = user_context.get("product_type", "")
        
        if product_type and node.node_type == NodeType.REQUIREMENTS:
            context_prompt = f"Adapt this question for a {product_type} product: {base_question}"
            try:
                result = await self.llm.ainvoke(context_prompt)
                return result.content
            except:
                return base_question
        
        return base_question
    
    def _update_prd_section(self, section_name: str, content: str):
        """Update PRD document section"""
        section_mapping = {
            "executive_summary": self.prd_document.executive_summary,
            "background_context": self.prd_document.background_context,
            "product_definition": self.prd_document.product_definition,
            "functional_requirements": self.prd_document.functional_requirements,
            "non_functional_requirements": self.prd_document.non_functional_requirements,
            "technical_requirements": self.prd_document.technical_requirements,
            "user_experience": self.prd_document.user_experience,
            "implementation_plan": self.prd_document.implementation_plan,
            "success_criteria": self.prd_document.success_criteria,
            "constraints_assumptions": self.prd_document.constraints_assumptions
        }
        
        if section_name in section_mapping:
            section = section_mapping[section_name]
            if section.content:
                section.content += f"\n\n{content}"
            else:
                section.content = content
            section.last_updated = datetime.now()
            section.completeness = min(100.0, len(section.content) / 500 * 100)  # Simple completeness metric
    
    async def _generate_final_prd(self, state: ConversationState) -> str:
        """Generate the final PRD document"""
        prd_generation_prompt = """
        Based on the conversation history and collected information, generate a comprehensive PRD document following this structure:

        # Product Requirements Document

        ## 1. Executive Summary
        {executive_summary}

        ## 2. Background & Context
        {background_context}

        ## 3. Product Definition
        {product_definition}

        ## 4. Functional Requirements
        {functional_requirements}

        ## 5. Non-Functional Requirements
        {non_functional_requirements}

        ## 6. Technical Requirements
        {technical_requirements}

        ## 7. User Experience & Design
        {user_experience}

        ## 8. Implementation Plan
        {implementation_plan}

        ## 9. Success Criteria & Metrics
        {success_criteria}

        ## 10. Constraints & Assumptions
        {constraints_assumptions}

        Please ensure the document is professional, comprehensive, and follows best practices for PRD creation.
        """
        
        formatted_prompt = prd_generation_prompt.format(
            executive_summary=self.prd_document.executive_summary.content or "To be defined",
            background_context=self.prd_document.background_context.content or "To be defined",
            product_definition=self.prd_document.product_definition.content or "To be defined",
            functional_requirements=self.prd_document.functional_requirements.content or "To be defined",
            non_functional_requirements=self.prd_document.non_functional_requirements.content or "To be defined",
            technical_requirements=self.prd_document.technical_requirements.content or "To be defined",
            user_experience=self.prd_document.user_experience.content or "To be defined",
            implementation_plan=self.prd_document.implementation_plan.content or "To be defined",
            success_criteria=self.prd_document.success_criteria.content or "To be defined",
            constraints_assumptions=self.prd_document.constraints_assumptions.content or "To be defined"
        )
        
        try:
            result = await self.llm.ainvoke(formatted_prompt)
            return result.content
        except Exception as e:
            return f"Error generating PRD: {str(e)}"
    
    async def start_conversation(self) -> str:
        """Start the PRD generation conversation"""
        initial_state: ConversationState = {
            "messages": [],
            "current_node": "start",
            "prd_data": {},
            "retry_count": 0,
            "user_context": {},
            "validation_flags": {},
            "completeness_score": 0.0
        }
        
        # Generate initial response
        result = await self.graph.ainvoke(initial_state, config={"configurable": {"thread_id": "prd_session"}})
        
        if result["messages"]:
            return result["messages"][-1].content
        return "Welcome to the PRD Generator! Let's get started."
    
    async def process_user_response(self, user_input: str, thread_id: str = "prd_session") -> str:
        """Process user response and return next question"""
        # Get current state
        config = {"configurable": {"thread_id": thread_id}}
        current_state = self.graph.get_state(config)
        
        if current_state:
            state = current_state.values
            state["messages"].append(HumanMessage(content=user_input))
        else:
            state: ConversationState = {
                "